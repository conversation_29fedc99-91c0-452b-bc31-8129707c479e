<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skills Gap Heatmap Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #1a2b5f;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #2c3e50;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            position: relative;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Skills Gap Heatmap Test Suite</h1>
        <p>This page tests the Skills Gap heatmap fixes for PDF export quality and zoom mode label truncation.</p>
        
        <div class="test-section">
            <h2>Test Data Generation</h2>
            <button class="test-button" onclick="generateSmallDataset()">Generate Small Dataset (5x5)</button>
            <button class="test-button" onclick="generateMediumDataset()">Generate Medium Dataset (10x10)</button>
            <button class="test-button" onclick="generateLargeDataset()">Generate Large Dataset (20x15)</button>
            <button class="test-button" onclick="generateLongLabelsDataset()">Generate Long Labels Dataset</button>
            <div id="data-results" class="results"></div>
        </div>

        <div class="test-section">
            <h2>Chart Rendering Test</h2>
            <div class="chart-container">
                <canvas id="testSkillGapChart"></canvas>
            </div>
            <button class="test-button" onclick="renderChart()">Render Chart</button>
            <button class="test-button" onclick="testExpandChart()">Test Expand Mode</button>
            <div id="chart-results" class="results"></div>
        </div>

        <div class="test-section">
            <h2>PDF Export Test (New Visualization Approach)</h2>
            <button class="test-button" onclick="testPDFVisualization()">Test PDF Pie Chart & Bar Chart</button>
            <button class="test-button" onclick="testGapDistribution()">Test Gap Distribution Calculation</button>
            <button class="test-button" onclick="testTopSkillGaps()">Test Top Skills Analysis</button>
            <div id="pdf-results" class="results"></div>
        </div>

        <div class="test-section">
            <h2>Label Truncation Test</h2>
            <button class="test-button" onclick="testLabelTruncation()">Test Label Truncation Logic</button>
            <button class="test-button" onclick="testFontSizeCalculation()">Test Font Size Calculation</button>
            <div id="label-results" class="results"></div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-matrix@2.0.1/dist/chartjs-chart-matrix.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    
    <script>
        // Test data storage
        let currentTestData = null;
        let testChart = null;

        // Test data generators
        function generateSmallDataset() {
            const roles = ['Developer', 'Designer', 'Manager', 'Analyst', 'Tester'];
            const skills = ['JavaScript', 'CSS', 'React', 'Node.js', 'Testing'];
            currentTestData = generateMatrixData(roles, skills);
            logResult('data-results', 'Generated small dataset (5x5)', 'success');
            logResult('data-results', JSON.stringify(currentTestData, null, 2), 'info');
        }

        function generateMediumDataset() {
            const roles = ['Frontend Dev', 'Backend Dev', 'Full Stack', 'DevOps', 'QA Engineer', 
                          'Product Manager', 'UI Designer', 'UX Designer', 'Data Analyst', 'Scrum Master'];
            const skills = ['JavaScript', 'Python', 'Java', 'React', 'Angular', 'Vue.js', 
                           'Node.js', 'Docker', 'AWS', 'Testing'];
            currentTestData = generateMatrixData(roles, skills);
            logResult('data-results', 'Generated medium dataset (10x10)', 'success');
        }

        function generateLargeDataset() {
            const roles = ['Frontend Developer', 'Backend Developer', 'Full Stack Developer', 'DevOps Engineer', 
                          'QA Engineer', 'Product Manager', 'UI Designer', 'UX Designer', 'Data Analyst', 
                          'Scrum Master', 'Technical Lead', 'Solution Architect', 'Business Analyst', 
                          'Project Manager', 'Security Engineer', 'Database Administrator', 'Mobile Developer', 
                          'Machine Learning Engineer', 'Cloud Engineer', 'Site Reliability Engineer'];
            const skills = ['JavaScript', 'Python', 'Java', 'React', 'Angular', 'Vue.js', 'Node.js', 
                           'Docker', 'AWS', 'Testing', 'Git', 'SQL', 'MongoDB', 'Redis', 'Kubernetes'];
            currentTestData = generateMatrixData(roles, skills);
            logResult('data-results', 'Generated large dataset (20x15)', 'success');
        }

        function generateLongLabelsDataset() {
            const roles = ['Senior Frontend Development Specialist', 'Backend API Development Engineer', 
                          'Full Stack Web Application Developer', 'DevOps Infrastructure Automation Engineer', 
                          'Quality Assurance Testing Specialist'];
            const skills = ['Advanced JavaScript ES6+ Development', 'Responsive CSS Grid and Flexbox Design', 
                           'React Component Architecture and State Management', 'Node.js Server-Side Development', 
                           'Automated Testing and Quality Assurance'];
            currentTestData = generateMatrixData(roles, skills);
            logResult('data-results', 'Generated long labels dataset', 'success');
        }

        function generateMatrixData(roles, skills) {
            const matrixData = [['Role', ...skills]];
            roles.forEach(role => {
                const row = [role];
                skills.forEach(() => {
                    row.push(Math.floor(Math.random() * 16)); // 0-15 random gap values
                });
                matrixData.push(row);
            });
            return matrixData;
        }

        // Chart rendering tests
        function renderChart() {
            if (!currentTestData) {
                logResult('chart-results', 'No test data available. Generate data first.', 'error');
                return;
            }

            try {
                // Destroy existing chart
                if (testChart) {
                    testChart.destroy();
                }

                // Create chart using similar logic to reports.js
                const ctx = document.getElementById('testSkillGapChart').getContext('2d');
                const headers = currentTestData[0];
                const roles = headers.slice(1);
                const skills = currentTestData.slice(1).map(row => row[0]);
                
                const matrixChartData = [];
                currentTestData.slice(1).forEach((row, skillIndex) => {
                    row.slice(1).forEach((value, roleIndex) => {
                        matrixChartData.push({
                            x: roles[roleIndex],
                            y: skills[skillIndex],
                            v: value
                        });
                    });
                });

                testChart = new Chart(ctx, {
                    type: 'matrix',
                    data: {
                        datasets: [{
                            label: 'Skill Gaps',
                            data: matrixChartData,
                            backgroundColor: function(ctx) {
                                const value = ctx.dataset.data[ctx.dataIndex]?.v || 0;
                                if (value === 0) return 'rgba(213,218,237,0.6)';
                                if (value <= 3) return 'rgba(151,174,248,0.8)';
                                if (value <= 6) return 'rgba(99,136,235,0.8)';
                                if (value <= 9) return 'rgba(44,95,215,0.8)';
                                if (value <= 12) return 'rgba(15,54,153,0.8)';
                                return 'rgba(10,36,102,0.9)';
                            },
                            width: 30,
                            height: 30
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'category',
                                position: 'bottom',
                                offset: true,
                                grid: { display: false },
                                ticks: { 
                                    font: { size: 11 },
                                    autoSkip: false,
                                    maxRotation: 45,
                                    callback: function(value, index) {
                                        const label = this.getLabelForValue(value);
                                        return label.length > 12 ? label.substring(0, 12) + '...' : label;
                                    }
                                },
                                labels: roles
                            },
                            y: {
                                type: 'category',
                                position: 'left',
                                offset: true,
                                grid: { display: false },
                                ticks: { 
                                    font: { size: 11 },
                                    autoSkip: false,
                                    callback: function(value, index) {
                                        const label = this.getLabelForValue(value);
                                        return label.length > 20 ? label.substring(0, 20) + '...' : label;
                                    }
                                },
                                labels: skills
                            }
                        }
                    }
                });

                logResult('chart-results', `Chart rendered successfully with ${roles.length} roles and ${skills.length} skills`, 'success');
            } catch (error) {
                logResult('chart-results', `Chart rendering failed: ${error.message}`, 'error');
            }
        }

        // Test expand chart functionality
        function testExpandChart() {
            if (!testChart) {
                logResult('chart-results', 'No chart available. Render chart first.', 'error');
                return;
            }

            try {
                // Test the font size calculation logic
                const roles = testChart.options.scales.x.labels || [];
                const skills = testChart.options.scales.y.labels || [];

                const xFontSize = calculateOptimalFontSize(roles.length, 'x');
                const yFontSize = calculateOptimalFontSize(skills.length, 'y');

                logResult('chart-results', `Expand mode font sizes: X-axis: ${xFontSize}px, Y-axis: ${yFontSize}px`, 'success');
                logResult('chart-results', `Labels count: ${roles.length} roles, ${skills.length} skills`, 'info');

                // Test label truncation
                const longRole = roles.find(r => r.length > 15);
                const longSkill = skills.find(s => s.length > 25);

                if (longRole) {
                    const truncated = longRole.length > 15 ? longRole.substring(0, 15) + '...' : longRole;
                    logResult('chart-results', `Role truncation test: "${longRole}" → "${truncated}"`, 'info');
                }

                if (longSkill) {
                    const truncated = longSkill.length > 25 ? longSkill.substring(0, 25) + '...' : longSkill;
                    logResult('chart-results', `Skill truncation test: "${longSkill}" → "${truncated}"`, 'info');
                }

            } catch (error) {
                logResult('chart-results', `Expand chart test failed: ${error.message}`, 'error');
            }
        }

        // Test new PDF visualization approach
        function testPDFVisualization() {
            if (!currentTestData) {
                logResult('pdf-results', 'No test data available. Generate data first.', 'error');
                return;
            }

            try {
                logResult('pdf-results', 'Testing new PDF visualization approach...', 'info');

                // Test gap distribution calculation
                const distribution = calculateGapDistribution(currentTestData);
                logResult('pdf-results', 'Gap Distribution:', 'success');
                Object.entries(distribution).forEach(([severity, count]) => {
                    if (count > 0) {
                        logResult('pdf-results', `  ${severity}: ${count} occurrences`, 'info');
                    }
                });

                // Test top skills calculation
                const topSkills = getTopSkillGaps(currentTestData, 5);
                logResult('pdf-results', 'Top 5 Skills with Highest Gaps:', 'success');
                topSkills.forEach((skill, index) => {
                    logResult('pdf-results', `  ${index + 1}. ${skill.skill}: Avg ${skill.averageGap.toFixed(1)}, Max ${skill.maxGap}`, 'info');
                });

                logResult('pdf-results', 'PDF visualization test completed successfully!', 'success');

            } catch (error) {
                logResult('pdf-results', `PDF visualization test failed: ${error.message}`, 'error');
            }
        }

        function testGapDistribution() {
            if (!currentTestData) {
                logResult('pdf-results', 'No test data available. Generate data first.', 'error');
                return;
            }

            const distribution = calculateGapDistribution(currentTestData);
            const total = Object.values(distribution).reduce((a, b) => a + b, 0);

            logResult('pdf-results', 'Gap Distribution Analysis:', 'info');
            logResult('pdf-results', `Total data points: ${total}`, 'info');

            Object.entries(distribution).forEach(([severity, count]) => {
                const percentage = total > 0 ? (count / total * 100).toFixed(1) : 0;
                logResult('pdf-results', `${severity}: ${count} (${percentage}%)`, count > 0 ? 'success' : 'info');
            });
        }

        function testTopSkillGaps() {
            if (!currentTestData) {
                logResult('pdf-results', 'No test data available. Generate data first.', 'error');
                return;
            }

            const topSkills = getTopSkillGaps(currentTestData, 8);

            logResult('pdf-results', 'Top Skills Gap Analysis:', 'info');
            topSkills.forEach((skill, index) => {
                logResult('pdf-results',
                    `${index + 1}. ${skill.skill}:\n   Avg: ${skill.averageGap.toFixed(2)}, Max: ${skill.maxGap}, Total: ${skill.totalGap}`,
                    'success');
            });
        }

        // Test label truncation logic
        function testLabelTruncation() {
            const testLabels = [
                'Short',
                'Medium Length Label',
                'Very Long Label That Should Be Truncated',
                'Extremely Long Label That Definitely Needs Truncation For Display'
            ];

            logResult('label-results', 'Testing label truncation logic:', 'info');

            testLabels.forEach(label => {
                const roleTruncated = label.length > 12 ? label.substring(0, 12) + '...' : label;
                const skillTruncated = label.length > 20 ? label.substring(0, 20) + '...' : label;
                const expandedRoleTruncated = label.length > 15 ? label.substring(0, 15) + '...' : label;
                const expandedSkillTruncated = label.length > 25 ? label.substring(0, 25) + '...' : label;

                logResult('label-results', `Original: "${label}"`, 'info');
                logResult('label-results', `  Regular role: "${roleTruncated}"`, 'info');
                logResult('label-results', `  Regular skill: "${skillTruncated}"`, 'info');
                logResult('label-results', `  Expanded role: "${expandedRoleTruncated}"`, 'info');
                logResult('label-results', `  Expanded skill: "${expandedSkillTruncated}"`, 'info');
                logResult('label-results', '', 'info');
            });
        }

        // Test font size calculation
        function testFontSizeCalculation() {
            const testCases = [
                { count: 3, axis: 'x', expected: 16 },
                { count: 8, axis: 'x', expected: 14 },
                { count: 15, axis: 'x', expected: 10 },
                { count: 25, axis: 'x', expected: 8 },
                { count: 5, axis: 'y', expected: 16 },
                { count: 12, axis: 'y', expected: 12 },
                { count: 18, axis: 'y', expected: 10 },
                { count: 30, axis: 'y', expected: 8 }
            ];

            logResult('label-results', 'Testing font size calculation:', 'info');

            testCases.forEach(testCase => {
                const calculated = calculateOptimalFontSize(testCase.count, testCase.axis);
                const status = calculated === testCase.expected ? 'success' : 'error';
                logResult('label-results',
                    `${testCase.count} ${testCase.axis}-labels → ${calculated}px (expected ${testCase.expected}px)`,
                    status);
            });
        }

        // Helper functions (copied from reports.js)
        function calculateOptimalFontSize(labelCount, axis) {
            let baseFontSize = 14;
            let minFontSize = 8;
            let maxFontSize = 16;

            if (axis === 'x') {
                if (labelCount <= 5) return maxFontSize;
                if (labelCount <= 8) return 14;
                if (labelCount <= 12) return 12;
                if (labelCount <= 16) return 10;
                return minFontSize;
            } else {
                if (labelCount <= 6) return maxFontSize;
                if (labelCount <= 10) return 14;
                if (labelCount <= 15) return 12;
                if (labelCount <= 20) return 10;
                return minFontSize;
            }
        }

        function calculateGapDistribution(matrixData) {
            const distribution = {
                'No Gap (0)': 0,
                'Minor (1-3)': 0,
                'Moderate (4-6)': 0,
                'Significant (7-9)': 0,
                'Severe (10-12)': 0,
                'Critical (13+)': 0
            };

            // Count gaps by severity
            for (let i = 1; i < matrixData.length; i++) {
                for (let j = 1; j < matrixData[i].length; j++) {
                    const value = matrixData[i][j] || 0;
                    if (value === 0) distribution['No Gap (0)']++;
                    else if (value <= 3) distribution['Minor (1-3)']++;
                    else if (value <= 6) distribution['Moderate (4-6)']++;
                    else if (value <= 9) distribution['Significant (7-9)']++;
                    else if (value <= 12) distribution['Severe (10-12)']++;
                    else distribution['Critical (13+)']++;
                }
            }

            return distribution;
        }

        function getTopSkillGaps(matrixData, topCount = 8) {
            const skillGaps = [];

            // Calculate average gap for each skill
            for (let i = 1; i < matrixData.length; i++) {
                const skillName = matrixData[i][0];
                const values = matrixData[i].slice(1).filter(v => v !== undefined && v !== null);
                const totalGap = values.reduce((sum, val) => sum + (val || 0), 0);
                const avgGap = values.length > 0 ? totalGap / values.length : 0;
                const maxGap = Math.max(...values.map(v => v || 0));

                skillGaps.push({
                    skill: skillName,
                    averageGap: avgGap,
                    maxGap: maxGap,
                    totalGap: totalGap
                });
            }

            // Sort by average gap and return top skills
            return skillGaps
                .sort((a, b) => b.averageGap - a.averageGap)
                .slice(0, topCount);
        }

        function getSkillGapColor(value) {
            if (value === 0) return { r: 213, g: 218, b: 237 };
            if (value <= 3) return { r: 151, g: 174, b: 248 };
            if (value <= 6) return { r: 99, g: 136, b: 235 };
            if (value <= 9) return { r: 44, g: 95, b: 215 };
            if (value <= 12) return { r: 15, g: 54, b: 153 };
            return { r: 10, g: 36, b: 102 };
        }

        // Helper function to log results
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            element.innerHTML += `<span class="${type}">${logEntry}</span>`;
            element.scrollTop = element.scrollHeight;
        }

        // Initialize with small dataset
        window.onload = function() {
            generateSmallDataset();
            renderChart();
        };
    </script>
</body>
</html>
