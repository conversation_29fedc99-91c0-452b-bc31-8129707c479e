(function(global) {
    let isModalInitialized = false;
    let isClosing = false;
    let currentChartInstance = null;
    let currentData = null;
    let currentAnalysisType = null;

    async function showSkillsGapAnalysis(data = null) {
        try {
            // Show loading overlay immediately
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            if (data) {
                currentData = data;  // Store the data

                // Determine which analysis type to show first
                if (currentData.metadata && currentData.metadata.availableAnalysisTypes) {
                    // Prioritize AI skills if available, then digital, then soft skills
                    if (currentData.metadata.availableAnalysisTypes.includes('aiSkills')) {
                        currentAnalysisType = 'aiSkills';
                    } else if (currentData.metadata.availableAnalysisTypes.includes('digitalSkills')) {
                        currentAnalysisType = 'digitalSkills';
                    } else {
                        currentAnalysisType = currentData.metadata.availableAnalysisTypes[0];
                    }
                } else if (currentData.aiSkills) {
                    currentAnalysisType = 'aiSkills';
                } else if (currentData.digitalSkills) {
                    currentAnalysisType = 'digitalSkills';
                } else if (currentData.softSkills) {
                    currentAnalysisType = 'softSkills';
                } else {
                    // Backward compatibility for old data structure
                    currentAnalysisType = 'legacy';
                }
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure
            const overlay = document.createElement('div');
            overlay.id = 'skills-gap-overlay';
            overlay.className = 'skills-modal-overlay';
            // Set initial opacity to 0 for fade-in effect
            overlay.style.opacity = '0';

            if (currentData) {
                // Determine if DISC section should be shown (completed, processing, or error states)
                let showDiscSection = false;
                try {
                    const active = getCurrentAnalysisData();
                    const email = (active && active.report && active.report.employeeEmail) || (currentData && currentData.metadata && currentData.metadata.userId);
                    const userCompany = (active && active.report && active.report.userCompany) || (currentData && currentData.metadata && currentData.metadata.userCompany) || 'Birmingham';
                    if (email && userCompany) {
                        const status = await getDiscAvailabilityStatus(email, userCompany);
                        showDiscSection = !!(status && status.shouldShow);
                    }
                } catch (e) {
                    // On errors checking availability, show section (error is a valid visible state)
                    showDiscSection = true;
                }
                overlay.innerHTML = createModalHTML(currentData, { showDiscSection });
            } else {
                throw new Error('Invalid data structure for modal creation');
            }

            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeEventListeners(overlay);

            // Create radar chart with actual data
            await createRadarChart(getCurrentAnalysisData());

            // Load DISC profile if user data is available
            try {
                const active = getCurrentAnalysisData();
                const email = (active && active.report && active.report.employeeEmail) || (currentData && currentData.metadata && currentData.metadata.userId);
                const userCompany = (active && active.report && active.report.userCompany) || (currentData && currentData.metadata && currentData.metadata.userCompany) || 'Birmingham';
                if (email && userCompany && document.getElementById('disc-profile-content')) {
                    console.log('Skills gap modal loading DISC profile:', {
                        email,
                        userCompany,
                        hasUserCompanyInData: !!(active && active.report && active.report.userCompany)
                    });
                    await loadDiscProfile(email, userCompany);
                    startDiscProfilePolling(email, userCompany);
                }
            } catch (e) {
                console.warn('DISC profile load skipped:', e && e.message ? e.message : e);
            }

            // Animate modal appearance with a smooth fade
            requestAnimationFrame(() => {
                if (!isClosing) {
                    // Use a small timeout to ensure smooth transition
                    setTimeout(() => {
                        overlay.style.opacity = '1';
                        const modalContent = overlay.querySelector('.skills-modal-content');
                        if (modalContent) {
                            modalContent.style.opacity = '1';
                            modalContent.style.transform = 'scale(1)';
                        }

                        // Hide loading overlay after modal is visible
                        if (typeof hideLoadingOverlay === 'function') {
                            hideLoadingOverlay();
                        }
                    }, 50);
                }
            });

            addStyles();
            if (typeof addDiscStyles === 'function') { addDiscStyles(); }
        } catch (error) {
            console.error('Error showing skills gap modal:', error);
            // Hide loading overlay if there's an error
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            throw error;
        }
    }

    function getCurrentAnalysisData() {
        if (currentAnalysisType === 'legacy') {
            return currentData; // Original structure for backward compatibility
        } else {
            return currentData[currentAnalysisType];
        }
    }

    async function resetAndShowModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new currentData (conditionally include DISC section)
        let showDiscSection = false;
        try {
            const active = getCurrentAnalysisData();
            const email = (active && active.report && active.report.employeeEmail) || (currentData && currentData.metadata && currentData.metadata.userId);
            const userCompany = (active && active.report && active.report.userCompany) || (currentData && currentData.metadata && currentData.metadata.userCompany) || 'Birmingham';
            if (email && userCompany) {
                const status = await getDiscAvailabilityStatus(email, userCompany);
                showDiscSection = !!(status && status.shouldShow);
            }
        } catch (_) { /* ignore and keep default false */ }
        overlay.innerHTML = createModalHTML(currentData, { showDiscSection });

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Recreate the chart
        await createRadarChart(getCurrentAnalysisData());

        // Load/refresh DISC profile if container exists
        try {
            const active = getCurrentAnalysisData();
            const email = (active && active.report && active.report.employeeEmail) || (currentData && currentData.metadata && currentData.metadata.userId);
            const userCompany = (active && active.report && active.report.userCompany) || (currentData && currentData.metadata && currentData.metadata.userCompany) || 'Birmingham';
            if (email && userCompany && document.getElementById('disc-profile-content')) {
                await loadDiscProfile(email, userCompany);
                startDiscProfilePolling(email, userCompany);
            }
        } catch (e) {
            console.warn('DISC profile refresh skipped:', e && e.message ? e.message : e);
        }

        // Show modal with improved animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0'; // Start with opacity 0

        requestAnimationFrame(() => {
            // Use a small timeout for smoother transition
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.skills-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }

                // Hide loading overlay after modal is visible
                if (typeof hideLoadingOverlay === 'function') {
                    hideLoadingOverlay();
                }
            }, 50);
        });
    }

    function createCompetencyCard(competency, data) {
        // Check if we have any strength areas
        const hasStrengths = Array.isArray(data.strengthAreas) && data.strengthAreas.length > 0;

        // Check if we have any gap areas
        const hasGaps = Array.isArray(data.gapAreas) && data.gapAreas.length > 0;

        // Create the strength areas HTML if we have any
        const strengthAreasHTML = hasStrengths
            ? `
                <div class="skills-strength-areas">
                    <h4>Strengths</h4>
                    ${data.strengthAreas.map(area => `
                        <span class="skills-badge skills-strength">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the gap areas HTML if we have any
        const gapAreasHTML = hasGaps
            ? `
                <div class="skills-gap-areas">
                    <h4>Gaps</h4>
                    ${data.gapAreas.map(area => `
                        <span class="skills-badge skills-gap">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the no data message if we don't have any strengths or gaps
        const noDataHTML = !hasStrengths && !hasGaps
            ? `<div class="skills-no-data">No data available</div>`
            : '';

        return `
            <div class="skills-competency-card">
                <h3>${competency}</h3>
                <div class="skills-proficiency-meter">
                    <div class="skills-progress-bar">
                        <div class="skills-progress" style="width: ${data.proficiencyLevel}"></div>
                    </div>
                    <span class="skills-proficiency-level">${data.proficiencyLevel}</span>
                </div>
                <div class="skills-areas-section">
                    ${strengthAreasHTML}
                    ${gapAreasHTML}
                    ${noDataHTML}
                </div>
            </div>
        `;
    }

    async function createRadarChart(data = null) {
        try {
            if (!data || !data.report || !data.report.competencyAnalysis) {
                throw new Error('Invalid data for radar chart creation');
            }

            // Cleanup existing chart if any
            if (currentChartInstance) {
                currentChartInstance.destroy();
                currentChartInstance = null;
            }

            const chartData = Object.entries(data.report.competencyAnalysis).map(([name, compData]) => ({
                axis: name,
                value: parseInt(compData.proficiencyLevel) || 0
            }));

            // Wait for Chart.js if not loaded
            if (!window.Chart) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            const container = document.querySelector('#radar-chart');
            if (!container) {
                throw new Error('Radar chart container not found');
            }

            // Clear container and create new canvas
            container.innerHTML = '';
            const ctx = document.createElement('canvas');
            container.appendChild(ctx);

            // Use different colors based on analysis type
            let borderColor, backgroundColor;

            if (currentAnalysisType === 'softSkills') {
                borderColor = '#7c3aed'; // Purple for soft skills
                backgroundColor = 'rgba(124, 58, 237, 0.1)';
            } else {
                borderColor = '#1e3a8a'; // Original blue color
                backgroundColor = 'rgba(30, 58, 138, 0.1)';
            }

            // Create chart for UI display (original size)
            currentChartInstance = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: chartData.map(d => d.axis),
                    datasets: [{
                        data: chartData.map(d => d.value),
                        backgroundColor: backgroundColor,
                        borderColor: borderColor,
                        pointBackgroundColor: borderColor
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { display: false },
                            grid: { color: '#d1d5db' },
                            angleLines: { color: '#d1d5db' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating radar chart:', error);
            throw error;
        }
    }

    function createModalHTML(data, options = {}) {
        // For backward compatibility
        let activeData;
        let hasMultipleAnalysisTypes = false;
        let analysisTypes = [];

        if (data.metadata && data.metadata.availableAnalysisTypes) {
            analysisTypes = data.metadata.availableAnalysisTypes;
            hasMultipleAnalysisTypes = analysisTypes.length > 1;
            activeData = getCurrentAnalysisData();
        } else if (data.aiSkills || data.digitalSkills || data.softSkills) {
            // Handle new multi-analysis structure
            if (data.aiSkills) analysisTypes.push('aiSkills');
            if (data.digitalSkills) analysisTypes.push('digitalSkills');
            if (data.softSkills) analysisTypes.push('softSkills');
            hasMultipleAnalysisTypes = analysisTypes.length > 1;

            // Set activeData based on current analysis type
            if (currentAnalysisType === 'aiSkills' && data.aiSkills) {
                activeData = data.aiSkills;
            } else if (currentAnalysisType === 'digitalSkills' && data.digitalSkills) {
                activeData = data.digitalSkills;
            } else if (currentAnalysisType === 'softSkills' && data.softSkills) {
                activeData = data.softSkills;
            } else {
                // Fallback to first available
                activeData = data.aiSkills || data.digitalSkills || data.softSkills;
            }
        } else {
            activeData = data; // Original structure for backward compatibility
        }

        // Create analysis switcher UI if multiple types are available
        const analysisSwitcher = hasMultipleAnalysisTypes
            ? `
            <div class="skills-analysis-switcher">
                ${analysisTypes.map(type => `
                    <button
                        class="skills-analysis-btn${type === currentAnalysisType ? ' active' : ''}"
                        data-analysis-type="${type}">
                        ${getAnalysisTypeLabel(type)}
                    </button>
                `).join('')}
            </div>
            `
            : '';

        const competencyCards = activeData
            ? Object.entries(activeData.report.competencyAnalysis)
                  .map(([competency, competencyData]) =>
                      createCompetencyCard(competency, competencyData)
                  )
                  .join('')
            : '';

        // More flexible conditions for showing the enrollment button
        const shouldShowEnrollButton = activeData
            && activeData.report
            && (activeData.report.learningPath || activeData.report.currentPath || currentData.metadata?.currentPath);

        const enrollmentButton = shouldShowEnrollButton
            ? `
            <div class="skills-cta-section">
                <button id="enroll-now-button" class="skills-enroll-now-button">Start Assigning This Training</button>
            </div>
            `
            : '';

        // Core recommendations
        const coreRecommendations = activeData.recommendations.map(rec => `
            <li>
                <strong>${rec.course}</strong>
                <p>${rec.reason}</p>
            </li>
        `).join('');

        // Additional (collapsible) recommendations
        const crossPathSection = activeData.other_learning_paths_courses && activeData.other_learning_paths_courses.length > 0
            ? `
                <div class="skills-recommendations-section skills-cross-path-recommendations">
                    <div class="skills-cross-path-header">
                        <h3>Additional Learning Path Recommendations</h3>
                        <button id="toggle-cross-paths-btn" class="toggle-cross-paths-btn">Show/Hide</button>
                    </div>
                    <div id="skills-cross-paths-collapsible" class="skills-cross-paths-collapsible">
                        <ul>
                            ${
                                activeData.other_learning_paths_courses.map(rec => `
                                    <li>
                                        <strong>${rec.course}</strong>
                                        <p>${rec.reason}</p>
                                    </li>
                                `).join('')
                            }
                        </ul>
                    </div>
                </div>
            `
            : '';

        return `
            <div class="skills-modal-content">
                <div class="skills-modal-header">
                    <div class="skills-modal-title-container">
                        <h2 class="skills-modal-employee-title">
                            ${activeData && activeData.report ? `${activeData.report.employeeName || ''} ${activeData.report.role ? `- ${activeData.report.role}` : ''}` : ''}
                        </h2>
                        <h3 class="skills-modal-subtitle">Skills Gap Analysis Report</h3>
                    </div>
                    <div class="skills-modal-actions">
                        <button id="export-pdf-button" class="skills-export-pdf-button">Export to PDF</button>
                        <button id="close-skills-modal" class="skills-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="skills-modal-body">
                    ${analysisSwitcher}

                    <div id="radar-chart" class="skills-chart-container"></div>

                    <div class="skills-competency-grid">
                        ${competencyCards}
                    </div>

                    <div class="skills-learning-path-section">
                        <h3>Learning Path Recommendations</h3>
                        <p>${activeData ? activeData.report.summary : ''}</p>
                    </div>

                    <div class="skills-recommendations-container">
                        <div class="skills-recommendations-section skills-primary-recommendations">
                            <h3>Core Learning Path Recommendations</h3>
                            <ul>
                                ${coreRecommendations}
                            </ul>
                        </div>
                        ${crossPathSection}
                    </div>

                    ${options.showDiscSection ? `
                    <div class="skills-disc-section">
                        <h3>DISC Behavioral Profile</h3>
                        <div id="disc-profile-content" class="disc-profile-content">
                            <div class="disc-pending">
                                <p>Loading DISC profile...</p>
                            </div>
                        </div>
                    </div>` : ''}

                    ${enrollmentButton}
                </div>
            </div>
        `;
    }

    function getAnalysisTypeLabel(type) {
        switch (type) {
            case 'digitalSkills':
                return 'Digital Skills';
            case 'softSkills':
                return 'Soft Skills';
            case 'aiSkills':
                return 'AI Skills';
            default:
                return type.charAt(0).toUpperCase() + type.slice(1).replace(/([A-Z])/g, ' $1');
        }
    }
    // DISC integration (minimal)
    let discPollingInterval = null;

    async function getDiscAvailabilityStatus(email, userCompany) {
        const dbRef = (typeof db !== 'undefined') ? db : ((typeof firebase !== 'undefined' && typeof firebase.firestore === 'function') ? firebase.firestore() : null);
        if (!dbRef) {
            return { shouldShow: true, state: 'error', error: 'Database not initialized' };
        }
        try {
            const userRef = dbRef.collection('companies').doc(userCompany).collection('users').doc(email);
            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                return { shouldShow: false, state: 'not_found' };
            }
            const discProfile = userDoc.data().discProfile;
            if (!discProfile) {
                return { shouldShow: false, state: 'missing' };
            }
            if (discProfile.status === 'completed') return { shouldShow: true, state: 'completed', discProfile };
            if (discProfile.status === 'processing') return { shouldShow: true, state: 'processing', discProfile };
            // Any other explicit status -> treat as error but visible
            return { shouldShow: true, state: 'error', discProfile };
        } catch (error) {
            return { shouldShow: true, state: 'error', error: error && error.message ? error.message : String(error) };
        }
    }

    async function loadDiscProfile(email, userCompany) {
        const discContent = document.getElementById('disc-profile-content');

        const dbRef = (typeof db !== 'undefined') ? db : ((typeof firebase !== 'undefined' && typeof firebase.firestore === 'function') ? firebase.firestore() : null);
        if (!dbRef) {
            if (discContent) {
                discContent.innerHTML = '<div class="disc-pending"><p>Database not initialized</p></div>';
            }
            return { shouldShow: true, state: 'error', error: 'Database not initialized' };
        }

        try {
            const userRef = dbRef.collection('companies').doc(userCompany).collection('users').doc(email);
            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                // No user -> do not show section
                return { shouldShow: false, state: 'not_found' };
            }
            const discProfile = userDoc.data().discProfile;
            // Debug: verify structured JSON fetch and professionalDevelopment data
            try {
                const da = discProfile && discProfile.detailedAnalysis;
                const pd = da && da.professionalDevelopment;
                console.debug('DISC fetch debug', {
                    hasDiscProfile: !!discProfile,
                    hasDetailedAnalysis: !!da,
                    professionalDevelopmentType: Array.isArray(pd) ? 'array' : typeof pd,
                    professionalDevelopmentLength: Array.isArray(pd) ? pd.length : undefined,
                    professionalDevelopmentSample: Array.isArray(pd) ? pd.slice(0, 2) : pd
                });
            } catch (e) {
                console.debug('DISC fetch debug logging error', e);
            }
            if (discProfile && discProfile.status === 'completed') {
                if (discContent) displayDiscProfile(discProfile);
                return { shouldShow: true, state: 'completed', discProfile };
            } else if (discProfile && discProfile.status === 'processing') {
                if (discContent) {
                    discContent.innerHTML = `
                        <div class="disc-loading">
                            <div class="disc-spinner"></div>
                            <p>Analyzing behavioral assessment...</p>
                            <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()" class="refresh-btn">Refresh</button>
                        </div>
                    `;
                }
                return { shouldShow: true, state: 'processing', discProfile };
            } else if (discProfile) {
                if (discContent) {
                    discContent.innerHTML = `
                        <div class="disc-error">
                            <p>Unable to display DISC profile</p>
                            <small>Unknown status</small>
                            <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()" class="retry-btn">Try Again</button>
                        </div>
                    `;
                }
                return { shouldShow: true, state: 'error', discProfile };
            } else {
                // No profile field -> hide section
                return { shouldShow: false, state: 'missing' };
            }
        } catch (error) {
            console.error('Error loading DISC profile:', error);
            if (discContent) {
                discContent.innerHTML = `
                    <div class="disc-error">
                        <p>Error loading DISC profile</p>
                        <small>${error.message || 'Unknown error'}</small>
                        <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()" class="retry-btn">Try Again</button>
                    </div>
                `;
            }
            return { shouldShow: true, state: 'error', error: error && error.message ? error.message : String(error) };
        }
    }

    function displayDiscProfile(discProfile) {
        const discContent = document.getElementById('disc-profile-content');
        if (!discContent) return;

        const primaryType = discProfile.primaryType;
        const typeMap = { D: 'Dominance', I: 'Influence', S: 'Steadiness', C: 'Conscientiousness' };
        if (!typeMap[primaryType]) {
            discContent.innerHTML = '<div class="disc-pending"><p>Invalid DISC profile data</p></div>';
            return;
        }
        const fullTypeName = typeMap[primaryType];

        discContent.innerHTML = `
            <div class="disc-profile-completed">
                <div class="disc-badge ${primaryType}">${primaryType}</div>
                <div class="disc-info">
                    <h4>Primary Type: ${primaryType} - ${fullTypeName}</h4>
                    <div class="disc-scores">
                        <div class="score-item"><span class="score-label">Dominance</span><span class="score-value">${discProfile.scores?.D || 0}%</span></div>
                        <div class="score-item"><span class="score-label">Influence</span><span class="score-value">${discProfile.scores?.I || 0}%</span></div>
                        <div class="score-item"><span class="score-label">Steadiness</span><span class="score-value">${discProfile.scores?.S || 0}%</span></div>
                        <div class="score-item"><span class="score-label">Conscientiousness</span><span class="score-value">${discProfile.scores?.C || 0}%</span></div>
                    </div>
                    <button class="disc-learn-more" onclick="toggleDiscDetails()">Learn More</button>
                    <div id="disc-detailed-content" class="disc-detailed-content" style="display:none;">
                        ${formatDiscReport(discProfile, primaryType, fullTypeName)}
                    </div>
                </div>
            </div>
        `;
    }

    function formatDiscReport(discProfile, primaryType, fullTypeName) {
        // Icons for known sections
        const sectionIcons = {
            'OVERVIEW': '👤', 'STRENGTHS': '💪', 'POTENTIAL CHALLENGES': '⚠️', 'CHALLENGES': '⚠️',
            'COMMUNICATION STYLE': '💬', 'COMMUNICATION': '💬', 'WORK ENVIRONMENT': '🏢', 'WORK': '🏢',
            'LEADERSHIP STYLE': '👑', 'LEADERSHIP': '👑', 'TEAM DYNAMICS': '🤝', 'TEAM': '🤝',
            'PROFESSIONAL DEVELOPMENT': '📈', 'DEVELOPMENT': '📈'
        };

        const intro = `
            <div class="disc-analysis-intro">
                <h5>Detailed Behavioral Analysis</h5>
                <p>Comprehensive insights into your ${fullTypeName} personality profile</p>
            </div>
        `;

        if (!discProfile) {
            return `
                <div class="disc-analysis-intro">
                    <h5>Detailed Analysis</h5>
                    <p>Detailed behavioral analysis is being generated. Please check back shortly.</p>
                    <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()" class="refresh-btn">Refresh</button>
                </div>
            `;
        }

        const hasStructured = !!discProfile.detailedAnalysis && typeof discProfile.detailedAnalysis === 'object';
        const hasLegacy = !!discProfile.detailedReport;

        if (hasStructured) {
            return intro + formatStructuredAnalysis(discProfile.detailedAnalysis, sectionIcons);
        }
        if (hasLegacy) {
            return intro + parseAlternativeFormat(String(discProfile.detailedReport || ''), sectionIcons);
        }
        return intro + '<p>No detailed analysis available</p>';
    }

    // Helpers for advanced DISC section formatting
    function formatSectionContent(content) {
        const lines = String(content).split('\n');
        let html = '';
        let inList = false;
        const closeList = () => { if (inList) { html += '</ul>'; inList = false; } };
        for (let raw of lines) {
            const line = raw.trim();
            if (!line) { closeList(); continue; }
            const bulletMatch = line.match(/^[-*]\s+(.+)/);
            const numMatch = line.match(/^\d+\.\s+(.+)/);
            if (bulletMatch || numMatch) {
                if (!inList) { html += '<ul class="disc-bullets">'; inList = true; }
                const text = bulletMatch ? bulletMatch[1] : numMatch[1];
                html += `<li>${formatInlineText(text)}</li>`;
            } else {
                closeList();
                html += `<p>${formatInlineText(line)}</p>`;
            }
        }
        closeList();
        return html;
    }

    function formatInlineText(text) {
        // Escape HTML first
        let t = String(text).replace(/</g, '&lt;');
        // Inline code
        t = t.replace(/`([^`]+)`/g, '<code>$1</code>');
        // Bold and italic
        t = t.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        t = t.replace(/\*([^*]+)\*/g, '<em>$1</em>');
        // Highlight DISC-related terms
        t = t.replace(/\b(DISC|Dominance|Influence|Steadiness|Conscientiousness|D|I|S|C)\b/g, '<span class="disc-term">$1</span>');
        return t;
    }


    // Toggle expand/collapse
    window.toggleDiscDetails = function() {
        const details = document.getElementById('disc-detailed-content');
        const btn = document.querySelector('.disc-learn-more');
        if (!details || !btn) return;
        const expanded = details.style.display !== 'none';
        details.style.display = expanded ? 'none' : 'block';
        btn.textContent = expanded ? 'Learn More' : 'Show Less';
        btn.classList.toggle('expanded', !expanded);
    };

    window.refreshDiscProfile = async function() {
        try {
            const active = getCurrentAnalysisData();
            const email = (active && active.report && active.report.employeeEmail) || (currentData && currentData.metadata && currentData.metadata.userId);
            const userCompany = (active && active.report && active.report.userCompany) || (currentData && currentData.metadata && currentData.metadata.userCompany) || 'Birmingham';
            if (email && userCompany) {
                await loadDiscProfile(email, userCompany);
            }
        } catch (e) {
            console.warn('Manual DISC refresh skipped:', e && e.message ? e.message : e);
        }
    };

    function startDiscProfilePolling(email, userCompany) {
        clearDiscPolling();
        const content = document.getElementById('disc-profile-content');
        if (!content) return;
        const isProcessing = content.innerHTML.includes('Analyzing behavioral assessment');
        if (!isProcessing) return;
        discPollingInterval = setInterval(async () => {

            await loadDiscProfile(email, userCompany);
            const updated = document.getElementById('disc-profile-content');
            if (updated && !updated.innerHTML.includes('disc-spinner')) {
                clearDiscPolling();
            }
        }, 3000);
        setTimeout(clearDiscPolling, 120000);
    }

    function clearDiscPolling() {
        if (discPollingInterval) {
            clearInterval(discPollingInterval);
            discPollingInterval = null;
        }
    }

    // Structured JSON rendering (preferred when available)
    function formatStructuredAnalysis(analysis, sectionIcons) {
        let html = '';
        const sections = [
            ['overview', 'Overview'],
            ['strengths', 'Strengths'],
            ['challenges', 'Potential Challenges'],
            ['communicationStyle', 'Communication Style'],
            ['workEnvironment', 'Work Environment'],
            ['leadershipStyle', 'Leadership Style'],
            ['teamDynamics', 'Team Dynamics'],
            ['professionalDevelopment', 'Professional Development']
        ];

        for (const [key, title] of sections) {
            const value = analysis && analysis[key];
            const isEmptyArray = Array.isArray(value) && value.length === 0;
            const isEmptyString = typeof value === 'string' && value.trim() === '';
            if (value == null || isEmptyArray || isEmptyString) continue;

            const contentHtml = formatSectionData(value);
            if (!contentHtml) continue;

            const upper = title.toUpperCase();
            const icon = sectionIcons[upper] || '📋';
            html += `
                <div class="disc-section">
                    <div class="disc-section-header">
                        <div class="disc-section-icon">${icon}</div>
                        <h4 class="disc-section-title">${title}</h4>
                    </div>
                    <div class="disc-section-content">${contentHtml}</div>
                </div>
            `;
        }
        return html;
    }

    function formatSectionData(data) {
        if (Array.isArray(data)) {
            if (data.length === 0) return '';
            const items = [];
            for (const item of data) {
                if (item && typeof item === 'object') {
                    // Support both {title, description} and {area, recommendation}
                    const areaRaw = item.area != null ? String(item.area).trim() : '';
                    const recRaw = item.recommendation != null ? String(item.recommendation).trim() : '';
                    let combined = '';
                    if (areaRaw || recRaw) {
                        const area = areaRaw ? `<strong>${formatInlineText(areaRaw)}</strong>` : '';
                        const rec = recRaw ? formatInlineText(recRaw) : '';
                        combined = area && rec ? `${area} — ${rec}` : (area || rec);
                    } else {
                        const t = item.title != null ? String(item.title).trim() : '';
                        const d = item.description != null ? String(item.description).trim() : '';
                        const title = t ? `<strong>${formatInlineText(t)}</strong>` : '';
                        const desc = d ? formatInlineText(d) : '';
                        combined = title && desc ? `${title} — ${desc}` : (title || desc);
                    }
                    if (!combined) continue;
                    items.push(`<li>${combined}</li>`);
                } else {
                    const text = String(item).trim();
                    if (!text) continue;
                    items.push(`<li>${formatInlineText(text)}</li>`);
                }
            }
            if (items.length === 0) return '';
            return `<ul class="disc-bullets">${items.join('')}</ul>`;
        }
        if (typeof data === 'string') {
            const s = data.trim();
            return s ? formatSectionContent(s) : '';
        }
        if (data && typeof data === 'object') {
            const items = [];
            for (const [k, v] of Object.entries(data)) {
                const raw = v != null ? String(v).trim() : '';
                if (!raw) continue;
                const label = String(k).replace(/([A-Z])/g, ' $1').replace(/^./, s => s.toUpperCase());
                items.push(`<li><strong>${label}:</strong> ${formatInlineText(raw)}</li>`);
            }
            if (items.length === 0) return '';
            return `<ul class="disc-bullets">${items.join('')}</ul>`;
        }
        return '';
    }

    // Legacy text fallback parsing (kept for backward compatibility)
    function parseAlternativeFormat(detailedReport, sectionIcons) {
        let html = '';
        const sections = String(detailedReport).split(/\n\s*\n/);
        sections.forEach(section => {
            const lines = section.trim().split('\n');
            if (lines.length === 0) return;

            const firstLine = lines[0].trim();
            const headerMatch = firstLine.match(/^\d+\.\s*(.*?)$/i) ||
                firstLine.match(/^(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT).*$/i);

            if (headerMatch) {
                const sectionTitle = (headerMatch[1] || headerMatch[0]).toUpperCase().trim();
                const cleanTitle = sectionTitle.replace(/^\d+\.\s*/, '');
                const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

                html += `
                    <div class="disc-section">
                        <div class="disc-section-header">
                            <div class="disc-section-icon">${icon}</div>
                            <h4 class="disc-section-title">${displayTitle}</h4>
                        </div>
                `;

                if (lines.length > 1) {
                    const content = lines.slice(1).join('\n').trim();
                    html += `<div class=\"disc-section-content\">${formatSectionContent(content)}</div>`;
                }

                html += `</div>`;
            } else if (section.trim().length > 0) {
                html += `
                    <div class="disc-section">
                        <div class="disc-section-header">
                            <div class="disc-section-icon">📝</div>
                            <h4 class="disc-section-title">Additional Insights</h4>
                        </div>
                        <div class="disc-section-content">${formatSectionContent(section)}</div>
                    </div>
                `;
            }
        });
        return html;
    }

    // Comprehensive styles for DISC section (from DISC integration spec)
    function addDiscStyles() {
        const style = document.createElement('style');
        style.textContent = `
        .skills-disc-section { margin-top: 1.5rem; }
        .disc-profile-content { margin-top: .5rem; }

        /* Unified DISC Details Styles */
        .disc-detailed-content {
            display: block;
            margin-top: 1rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.04);
            animation: fadeIn .4s ease;
        }
        .disc-analysis-intro h5 { margin: 0 0 .5rem 0; font-size: 1.05rem; color: #111827; }
        .disc-analysis-intro p { margin: 0; color: #4b5563; }

        /* Section Styling with Icons */
        .disc-section { background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: .75rem .9rem; margin-top: .75rem; }
        .disc-section + .disc-section { margin-top: .75rem; }
        .disc-section-header { display: flex; align-items: center; gap: .5rem; margin-bottom: .5rem; }
        .disc-section-icon { width: 28px; height: 28px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; background: #eef2ff; color: #3730a3; font-size: 14px; box-shadow: inset 0 0 0 1px #e0e7ff; }
        .disc-section-title { font-size: 1rem; font-weight: 700; color: #111827; margin: 0; }
        .disc-section-content { color: #374151; line-height: 1.55; }
        .disc-section-content p { margin: .4rem 0; }
        .disc-section-content ul.disc-bullets { margin: .4rem 0 .2rem .9rem; padding: 0; list-style: disc; }
        .disc-section-content li { margin: .2rem 0; }
        .disc-section-content code { background: #f3f4f6; border: 1px solid #e5e7eb; border-radius: 4px; padding: 0 .3rem; font-size: .85em; }
        .disc-term { font-weight: 600; color: #1f2937; background: #eef2ff; border: 1px solid #e5e7ff; padding: 0 .25rem; border-radius: 3px; }

        /* Enhanced Scores Display */
        .disc-scores { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: .6rem; margin: .9rem 0; }
        .score-item { background: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%); border: 1px solid #e5e7eb; border-radius: 10px; padding: .65rem; text-align: center; transition: transform .15s ease, box-shadow .15s ease; }
        .score-item:hover { transform: translateY(-2px); box-shadow: 0 6px 14px rgba(0,0,0,0.06); }
        .score-item .score-label { font-size: .74rem; color: #64748b; text-transform: uppercase; letter-spacing: .04em; }
        .score-item .score-value { display:block; margin-top:.2rem; font-weight: 800; font-size: 1.05rem; background: linear-gradient(90deg, #1d4ed8, #0ea5e9); -webkit-background-clip:text; background-clip:text; color: transparent; }

        /* Interactive Button with Shimmer */
        .disc-learn-more { position: relative; overflow: hidden; margin-top: .6rem; padding: .55rem .85rem; background: linear-gradient(180deg, #1e40af, #1e3a8a); color: #fff; border: 1px solid #1d4ed8; border-radius: 6px; cursor: pointer; box-shadow: 0 2px 6px rgba(30,64,175,0.25); transition: transform .15s ease, box-shadow .15s ease, background .2s ease; }
        .disc-learn-more:hover { transform: translateY(-1px); box-shadow: 0 6px 14px rgba(30,64,175,0.3); }
        .disc-learn-more.expanded { background: linear-gradient(180deg, #475569, #334155); border-color: #475569; }
        .disc-learn-more:before { content:''; position: absolute; top: 0; left: -150%; width: 150%; height: 100%; background: linear-gradient(120deg, rgba(255,255,255,0.0) 0%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.0) 100%); transform: skewX(-20deg); }
        .disc-learn-more:hover:before { transition: left .8s ease; left: 150%; }

        /* Loading Spinner */
        .disc-loading .disc-spinner { width: 22px; height: 22px; border: 3px solid #e5e7eb; border-top-color: #3b82f6; border-radius: 50%; animation: disc-spin 1s linear infinite; margin: .6rem auto; }

        /* Responsive */
        @media (max-width: 640px) {
          .disc-scores { grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); }
          .disc-section-header { flex-wrap: wrap; }
          .disc-section-title { font-size: .95rem; }
        }

        /* Animations */
        @keyframes fadeIn { from { opacity: 0; transform: translateY(4px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes disc-spin { to { transform: rotate(360deg); } }
        `;
        document.head.appendChild(style);
    }



    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .skills-modal-overlay {
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.4s ease;
                z-index: 1000;
            }

            .skills-modal-content {
                background: #ffffff;
                border-radius: 6px;
                width: 80%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.4s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                font-size: 0.875rem;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            .skills-modal-header {
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            /* Analysis type switcher */
            .skills-analysis-switcher {
                display: flex;
                justify-content: center;
                margin: 0 0 1.5rem 0;
                gap: 0.5rem;
            }

            .skills-analysis-btn {
                background: #f3f4f6;
                border: 1px solid #e5e7eb;
                border-radius: 20px;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #4b5563;
            }

            .skills-analysis-btn:hover {
                background: #e5e7eb;
            }

            .skills-analysis-btn.active {
                background: #1e3a8a;
                color: white;
                border-color: #1e3a8a;
            }

            .skills-analysis-btn.active[data-analysis-type="softSkills"] {
                background: #7c3aed;
                border-color: #7c3aed;
            }

            .skills-modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .skills-modal-employee-title {
                font-size: 1rem;
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .skills-modal-subtitle {
                font-size: 0.8rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            .skills-modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .skills-close-modal-button,
            .skills-export-pdf-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .skills-close-modal-button svg {
                stroke: #1e3a8a;
            }

            .skills-export-pdf-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            .skills-modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            .skills-chart-container {
                position: relative;
                height: 300px;
                width: 80%;
                margin: 0 auto 1.5rem;
            }

            .skills-competency-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }

            .skills-competency-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .skills-competency-card h3 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            /* Different color for soft skills competency cards */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-competency-card h3 {
                color: #7c3aed;
            }

            .skills-proficiency-meter {
                margin: 0.75rem 0;
                display: flex;
                align-items: center;
            }

            .skills-progress-bar {
                background: #e5e7eb;
                border-radius: 9999px;
                height: 6px;
                width: 100%;
                margin-right: 0.5rem;
                overflow: hidden;
            }

            .skills-progress {
                background: #f59e0b;
                border-radius: 9999px;
                height: 100%;
                transition: width 0.3s ease;
            }

            /* Different color for soft skills progress */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-progress {
                background: #8b5cf6;
            }

            .skills-proficiency-level {
                font-size: 0.8rem;
                color: #6b7280;
            }

            .skills-areas-section h4 {
                margin: 0.5rem 0 0.25rem;
                font-size: 0.8rem;
                font-weight: 600;
                color: #374151;
            }

            .skills-badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                margin: 0.25rem 0.25rem 0 0;
            }

            .skills-badge.skills-strength {
                background: #f0f9ff;
                color: #1e3a8a;
            }

            /* Different colors for soft skills badges */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-badge.skills-strength {
                background: #f5f3ff;
                color: #7c3aed;
            }

            .skills-badge.skills-gap {
                background: #fef3f2;
                color: #9b1c1c;
            }

            .skills-learning-path-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin: 0 1rem 1.5rem;
            }

            .skills-learning-path-section h3 {
                margin-top: 0;
                font-size: 0.9rem;
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            /* Different color for soft skills headers */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-learning-path-section h3 {
                color: #7c3aed;
            }

            .skills-recommendations-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                padding: 0 1rem;
            }

            .skills-recommendations-section {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid transparent;
            }

            .skills-recommendations-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .skills-recommendations-section li {
                border-bottom: 1px solid #e5e7eb;
                padding: 0.5rem 0;
            }

            .skills-recommendations-section li:last-child {
                border-bottom: none;
            }

            .skills-primary-recommendations {
                border-left-color: #1e3a8a;
            }

            /* Different color for soft skills primary recommendations */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-primary-recommendations {
                border-left-color: #7c3aed;
            }

            .skills-cross-path-recommendations {
                border-left-color: #059669;
            }

            .skills-recommendations-section h3 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #374151;
            }

            .skills-cross-path-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.25rem;
            }

            .toggle-cross-paths-btn {
                background: none;
                border: 1px solid #059669;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
                color: #059669;
            }

            .toggle-cross-paths-btn:hover {
                background: #059669;
                color: #fff;
            }

            .skills-cta-section {
                text-align: center;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }

            .skills-enroll-now-button {
                background: #1e3a8a;
                color: white;
                border: none;
                border-radius: 24px;
                padding: 0.75rem 2rem;
                cursor: pointer;
                font-weight: 600;
                font-size: 1rem;
                transition: background-color 0.2s ease, transform 0.2s ease;
                box-shadow: 0 4px 14px rgba(30,58,138,0.3);
            }

            /* Different color for soft skills enroll button */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-enroll-now-button {
                background: #7c3aed;
                box-shadow: 0 4px 14px rgba(124,58,237,0.3);
            }

            .skills-enroll-now-button:hover {
                background: #1e40af;
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(30,58,138,0.4);
            }

            /* Different hover color for soft skills enroll button */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-enroll-now-button:hover {
                background: #6d28d9;
                box-shadow: 0 6px 20px rgba(124,58,237,0.4);
            }

            .skills-cross-paths-collapsible {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
                opacity: 0;
            }

            .skills-cross-paths-collapsible.expanded {
                max-height: 1000px;
                opacity: 1;
                transition: max-height 0.5s ease-in, opacity 0.3s ease-in;
            }

            /* Analysis fade transition */
            .skills-analysis-container {
                transition: opacity 0.3s ease-in-out;
            }

            .skills-analysis-container.fading-out {
                opacity: 0;
            }

            .skills-analysis-container.fading-in {
                opacity: 1;
            }

            /* No data available message */
            .skills-no-data {
                color: #6b7280;
                font-size: 0.85rem;
                text-align: center;
                padding: 0.75rem 0;
                font-style: italic;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    function initializeEventListeners(overlay) {
        const closeButton = overlay.querySelector('#close-skills-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        overlay.addEventListener('click', overlayClickHandler);

        const exportPdfButton = overlay.querySelector('#export-pdf-button');
        if (exportPdfButton) {
            exportPdfButton.addEventListener('click', exportToPDF);
        }

        // Add event listener for enroll button with more robust handling
        const enrollButton = overlay.querySelector('#enroll-now-button');
        if (enrollButton) {
            // Don't pass a specific learning path - the handler will determine it from the active analysis type
            enrollButton.addEventListener('click', () => handleEnrollment());
        } else {
            console.log('Enrollment button not found in modal');
        }

        // Toggle cross-path recommendations with animation
        const toggleButton = overlay.querySelector('#toggle-cross-paths-btn');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                const collapsible = overlay.querySelector('#skills-cross-paths-collapsible');
                if (collapsible) {
                    collapsible.classList.toggle('expanded');
                    // Update initial display style if not set
                    if (collapsible.style.display === 'none') {
                        collapsible.style.display = 'block';
                    }
                }
            });
        }

        // Initialize analysis type switcher buttons
        const analysisBtns = overlay.querySelectorAll('.skills-analysis-btn');
        analysisBtns.forEach(btn => {
            btn.addEventListener('click', async () => {
                const newAnalysisType = btn.getAttribute('data-analysis-type');
                if (newAnalysisType && newAnalysisType !== currentAnalysisType) {
                    await switchAnalysisType(newAnalysisType);
                }
            });
        });

        // Set initial analysis type on the modal body for CSS styling
        const modalBody = overlay.querySelector('.skills-modal-body');
        if (modalBody) {
            modalBody.setAttribute('data-analysis-type', currentAnalysisType);
        }
    }

    async function switchAnalysisType(newType) {
        // Don't switch if it's the same type
        if (newType === currentAnalysisType) return;

        // Update the current type
        const oldType = currentAnalysisType;
        currentAnalysisType = newType;

        // Update active button state
        const buttons = document.querySelectorAll('.skills-analysis-btn');
        buttons.forEach(btn => {
            const btnType = btn.getAttribute('data-analysis-type');
            if (btnType === newType) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Get the modal body
        const modalBody = document.querySelector('.skills-modal-body');
        if (modalBody) {
            // Add fading out class
            modalBody.classList.add('fading-out');

            // Wait for fade out transition
            await new Promise(resolve => setTimeout(resolve, 300));

            // Update data attribute for CSS
            modalBody.setAttribute('data-analysis-type', newType);

            // Get the active data
            const activeData = getCurrentAnalysisData();

            // Update the competency cards
            const competencyGrid = modalBody.querySelector('.skills-competency-grid');
            if (competencyGrid && activeData?.report?.competencyAnalysis) {
                competencyGrid.innerHTML = Object.entries(activeData.report.competencyAnalysis)
                    .map(([competency, competencyData]) =>
                        createCompetencyCard(competency, competencyData)
                    )
                    .join('');
            }

            // Update summary
            const summaryElement = modalBody.querySelector('.skills-learning-path-section p');
            if (summaryElement && activeData?.report) {
                summaryElement.textContent = activeData.report.summary || '';
            }

            // Update recommendations
            const primaryRecommendationsElement = modalBody.querySelector('.skills-primary-recommendations ul');
            if (primaryRecommendationsElement && activeData?.recommendations) {
                primaryRecommendationsElement.innerHTML = activeData.recommendations.map(rec => `
                    <li>
                        <strong>${rec.course}</strong>
                        <p>${rec.reason}</p>
                    </li>
                `).join('');
            }

            // Update other path recommendations
            const otherPathRecommendationsElement = modalBody.querySelector('#skills-cross-paths-collapsible ul');
            if (otherPathRecommendationsElement && activeData?.other_learning_paths_courses) {
                otherPathRecommendationsElement.innerHTML = activeData.other_learning_paths_courses.map(rec => `
                    <li>
                        <strong>${rec.course}</strong>
                        <p>${rec.reason}</p>
                    </li>
                `).join('');
            }

            // Show/hide other path recommendations section
            const crossPathSection = modalBody.querySelector('.skills-cross-path-recommendations');
            if (crossPathSection) {
                crossPathSection.style.display = (activeData?.other_learning_paths_courses?.length > 0) ? 'block' : 'none';
            }

            // Update enrollment button with more robust handling
            const ctaSection = modalBody.querySelector('.skills-cta-section');
            const shouldShowEnrollButton = activeData &&
                activeData.report &&
                (activeData.report.learningPath || activeData.report.currentPath ||
                 currentData.metadata?.pathsByType?.[newType] || currentData.metadata?.currentPath);

            // If enrollment button should be shown but isn't present, add it
            if (shouldShowEnrollButton) {
                if (!ctaSection) {
                    // Create CTA section if it doesn't exist
                    const newCtaSection = document.createElement('div');
                    newCtaSection.className = 'skills-cta-section';
                    newCtaSection.innerHTML = `
                        <button id="enroll-now-button" class="skills-enroll-now-button">
                            Start Assigning This Training
                        </button>
                    `;
                    modalBody.appendChild(newCtaSection);

                    // Add event listener to the new button - no need to pass a specific path
                    const newEnrollButton = newCtaSection.querySelector('#enroll-now-button');
                    if (newEnrollButton) {
                        newEnrollButton.addEventListener('click', () => handleEnrollment());
                    }
                } else {
                    // Update existing button's event listener
                    const enrollButton = ctaSection.querySelector('#enroll-now-button');
                    if (enrollButton) {
                        // Remove old event listeners by cloning and replacing
                        const newButton = enrollButton.cloneNode(true);
                        enrollButton.parentNode.replaceChild(newButton, enrollButton);

                        // Add new event listener - no need to pass a specific path
                        newButton.addEventListener('click', () => handleEnrollment());
                    }

                    // Make sure the section is visible
                    ctaSection.style.display = 'block';
                }
            } else if (ctaSection) {
                // Hide the enrollment section if there's no relevant data
                ctaSection.style.display = 'none';
            }

            // Update chart with new data
            await createRadarChart(activeData);

            // Remove fading out class and add fading in
            modalBody.classList.remove('fading-out');
            modalBody.classList.add('fading-in');

            // Remove fading in class after transition
            setTimeout(() => {
                modalBody.classList.remove('fading-in');
            }, 300);
        }
    }

    function handleEnrollment() {
        // Get the active analysis type's learning path
        const activeData = getCurrentAnalysisData();

        // Log which analysis type is active for debugging
        console.log('Handling enrollment with active analysis type:', currentAnalysisType);

        // Get the appropriate learning path based on the active analysis type
        let pathToUse = null;

        // First try to get path from active analysis data
        if (activeData?.report?.learningPath) {
            pathToUse = activeData.report.learningPath;
            console.log(`Using ${currentAnalysisType} learning path:`, pathToUse);
        }
        // Then try the type-specific paths from metadata if available
        else if (currentData.metadata?.pathsByType &&
                 currentData.metadata.pathsByType[currentAnalysisType]) {
            pathToUse = currentData.metadata.pathsByType[currentAnalysisType];
            console.log(`Using ${currentAnalysisType} path from metadata:`, pathToUse);
        }
        // Fall back to general currentPath
        else if (currentData.metadata?.currentPath) {
            pathToUse = currentData.metadata.currentPath;
            console.log('Falling back to general currentPath:', pathToUse);
        }

        if (!pathToUse) {
            console.error('No learning path available for enrollment');
            if (typeof showNotification === 'function') {
                showNotification('Unable to determine appropriate learning path for enrollment', 'error');
            }
            return;
        }

        // Hide the modal first
        hideModal();

        // Get the main content element
        const mainContent = document.querySelector('#main-content');
        if (!mainContent) {
            console.error('Main content element not found');
            return;
        }

        // Get user data from current data with robust fallbacks
        const userData = {
            email: activeData?.report?.email || currentData?.metadata?.userId,
            name: activeData?.report?.employeeName || currentData?.metadata?.employeeName,
            company: currentData?.metadata?.userCompany || userCompany
        };

        console.log('Enrollment data:', { learningPath: pathToUse, userData, analysisType: currentAnalysisType });

        // Make sure we have the required data
        if (!userData.email || !userData.company || !pathToUse) {
            console.error('Missing required data for enrollment', { userData, learningPath: pathToUse });
            if (typeof showNotification === 'function') {
                showNotification('Unable to start enrollment process - missing data', 'error');
            }
            return;
        }

        // Convert user data to base64 to safely pass as URL parameter
        const encodedUserData = btoa(JSON.stringify({
            ...userData,
            // Include the analysis type that was used for better tracking
            analysisType: currentAnalysisType
        }));

        // Preserve the previous page in navigation state
        const previousState = navigationState?.stack?.[navigationState?.currentIndex];

        // Load the enrollment page
        if (typeof loadEnrollmentPage === 'function') {
            loadEnrollmentPage(mainContent, pathToUse, {
                highlightUser: encodedUserData,
                previousPage: previousState ? previousState.page : null,
                // Pass the analysis type as an additional parameter
                analysisType: currentAnalysisType
            });
        } else {
            console.error('loadEnrollmentPage function not found');
            if (typeof showNotification === 'function') {
                showNotification('Enrollment functionality is not available', 'error');
            }
        }
    }

    function overlayClickHandler(event) {
        if (event.target.id === 'skills-gap-overlay') {
            hideModal();
        }
    }

    async function hideModal() {
        isClosing = true;
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        overlay.style.opacity = '0';
        const modalContent = overlay.querySelector('.skills-modal-content');
        if (modalContent) {
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';
        }

        // Clean up chart instance
        if (currentChartInstance) {
            currentChartInstance.destroy();
            currentChartInstance = null;
        }

        await new Promise(resolve => setTimeout(resolve, 300));
        overlay.style.display = 'none';

        // Clean up DISC polling
        if (typeof discPollingInterval !== 'undefined' && discPollingInterval) {
            clearInterval(discPollingInterval);
            discPollingInterval = null;
        }
    }

    function showModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            overlay.style.opacity = '0'; // Start with opacity 0

            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.skills-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }

                // Hide loading overlay after modal is visible
                if (typeof hideLoadingOverlay === 'function') {
                    hideLoadingOverlay();
                }
            }, 50);
        }
    }

    async function createHighResChartImage(data) {
        return new Promise((resolve, reject) => {
            try {
                if (!data || !data.report || !data.report.competencyAnalysis) {
                    resolve(''); // Return empty string if no data
                    return;
                }

                const chartData = Object.entries(data.report.competencyAnalysis).map(([name, compData]) => ({
                    axis: name,
                    value: parseInt(compData.proficiencyLevel) || 0
                }));

                // Create a high-resolution canvas for PDF export
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set smaller size for more professional, minimalistic look
                const scale = 2;
                canvas.width = 300 * scale;
                canvas.height = 300 * scale;
                ctx.scale(scale, scale);

                // Use different colors based on analysis type
                let borderColor, backgroundColor;
                if (currentAnalysisType === 'softSkills') {
                    borderColor = '#7c3aed'; // Purple for soft skills
                    backgroundColor = 'rgba(124, 58, 237, 0.1)';
                } else {
                    borderColor = '#1e3a8a'; // Original blue color
                    backgroundColor = 'rgba(30, 58, 138, 0.1)';
                }

                // Create temporary chart for image generation
                const tempChart = new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: chartData.map(d => d.axis),
                        datasets: [{
                            data: chartData.map(d => d.value),
                            backgroundColor: backgroundColor,
                            borderColor: borderColor,
                            pointBackgroundColor: borderColor,
                            borderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: false,
                        maintainAspectRatio: true,
                        animation: false, // Disable animation for immediate rendering
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    display: false // Remove numbered labels
                                },
                                grid: {
                                    color: '#e5e7eb',
                                    lineWidth: 1
                                },
                                angleLines: {
                                    color: '#e5e7eb',
                                    lineWidth: 1
                                },
                                pointLabels: {
                                    font: {
                                        size: 16,
                                        weight: '700'
                                    },
                                    color: '#1f2937',
                                    padding: 15,
                                    callback: function(label) {
                                        // Wrap long labels for better readability
                                        if (label.length > 15) {
                                            const words = label.split(' ');
                                            if (words.length > 1) {
                                                const mid = Math.ceil(words.length / 2);
                                                return [words.slice(0, mid).join(' '), words.slice(mid).join(' ')];
                                            }
                                        }
                                        return label;
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: { display: false }
                        }
                    }
                });

                // Wait for chart to render, then convert to image
                setTimeout(() => {
                    try {
                        const imageData = canvas.toDataURL('image/png', 1.0);
                        tempChart.destroy(); // Clean up temporary chart
                        resolve(imageData);
                    } catch (error) {
                        tempChart.destroy();
                        reject(error);
                    }
                }, 100);

            } catch (error) {
                reject(error);
            }
        });
    }

    async function exportToPDF() {
        const activeData = getCurrentAnalysisData();

        if (!activeData || !activeData.report) {
            console.error('No data available to export.');
            return;
        }

        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'pt', 'a4');

        // Generate high-resolution chart image
        let chartImage = '';
        try {
            chartImage = await createHighResChartImage(activeData);
        } catch (err) {
            console.error('Error generating high-res chart image:', err);
        }

        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const leftMargin = 40;
        const rightMargin = 40;
        const topMargin = 50;
        const bottomMargin = 50;
        const contentWidth = pageWidth - leftMargin - rightMargin;

        // Helper function to check if content fits on current page
        function checkPageSpace(currentY, requiredSpace) {
            return currentY + requiredSpace <= pageHeight - bottomMargin;
        }

        // Helper function to add new page with consistent margins
        function addNewPage() {
            doc.addPage();
            return topMargin;
        }

        // Title & Header with analysis type
        doc.setFontSize(16);

        // Use appropriate color based on analysis type
        if (currentAnalysisType === 'softSkills') {
            doc.setTextColor(124, 58, 237); // Purple for soft skills
        } else {
            doc.setTextColor(30, 58, 138); // Blue for digital skills
        }

        const analysisTypeText = currentAnalysisType === 'softSkills' ? 'Soft Skills' :
                                 currentAnalysisType === 'digitalSkills' ? 'Digital Skills' :
                                 'Skills';

        doc.text(`${analysisTypeText} Gap Analysis Report`, pageWidth / 2, topMargin, { align: 'center' });

        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        let employeeName = activeData.report.employeeName || '';
        let role = activeData.report.role ? ` - ${activeData.report.role}` : '';
        doc.text(`${employeeName}${role}`, pageWidth / 2, topMargin + 20, { align: 'center' });

        // Insert the high-res chart image if available
        let yPos = topMargin + 40;
        if (chartImage) {
            // Check if chart fits on current page
            const chartHeight = 250;
            if (!checkPageSpace(yPos, chartHeight + 20)) {
                yPos = addNewPage();
            }

            // Use smaller, more professional chart size
            const imageWidth = 250;
            const imageHeight = 250;
            const xPos = (pageWidth - imageWidth) / 2; // Center the chart
            doc.addImage(chartImage, 'PNG', xPos, yPos, imageWidth, imageHeight);
            yPos += chartHeight + 20;
        }

        // Competency Table
        const competencyData = Object.entries(activeData.report.competencyAnalysis).map(([competency, compData]) => {
            return {
                competency: competency,
                proficiency: compData.proficiencyLevel,
                strengths: compData.strengthAreas.join(', '),
                gaps: compData.gapAreas.join(', ')
            };
        });

        // Check if we need a new page for the table
        const estimatedTableHeight = (competencyData.length + 1) * 25 + 60; // Rough estimate
        if (!checkPageSpace(yPos, estimatedTableHeight)) {
            yPos = addNewPage();
        }

        doc.setFontSize(12);
        if (currentAnalysisType === 'softSkills') {
            doc.setTextColor(124, 58, 237); // Purple for soft skills
        } else {
            doc.setTextColor(30, 58, 138); // Blue for digital skills
        }
        doc.text('Competency Analysis', leftMargin, yPos);
        yPos += 20;

        // Set table header color based on analysis type
        const headerColor = currentAnalysisType === 'softSkills' ?
            [124, 58, 237] : // Purple for soft skills
            [30, 58, 138];   // Blue for digital skills

        doc.autoTable({
            startY: yPos,
            head: [['Competency', 'Proficiency', 'Strengths', 'Gaps']],
            body: competencyData.map(row => [row.competency, row.proficiency, row.strengths, row.gaps]),
            styles: { fontSize: 10, cellPadding: 5 },
            headStyles: { fillColor: headerColor, textColor: [255, 255, 255] },
            margin: { left: leftMargin, right: rightMargin },
            pageBreak: 'auto',
            showHead: 'everyPage'
        });

        yPos = doc.autoTable.previous.finalY + 20;

        // Summary
        const summaryText = activeData.report.summary || '';
        if (summaryText) {
            // Check if summary section fits on current page
            const summaryLines = doc.splitTextToSize(summaryText, contentWidth);
            const summaryHeight = 35 + (summaryLines.length * 12);

            if (!checkPageSpace(yPos, summaryHeight)) {
                yPos = addNewPage();
            }

            doc.setFontSize(12);
            if (currentAnalysisType === 'softSkills') {
                doc.setTextColor(124, 58, 237); // Purple for soft skills
            } else {
                doc.setTextColor(30, 58, 138); // Blue for digital skills
            }
            doc.text('Summary', leftMargin, yPos);
            yPos += 15;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text(summaryLines, leftMargin, yPos);
            yPos += (summaryLines.length * 12) + 20;
        }

        // Primary Recommendations
        if (activeData.recommendations && activeData.recommendations.length > 0) {
            // Estimate space needed for recommendations section
            const estimatedRecsHeight = 60 + (activeData.recommendations.length * 40);

            if (!checkPageSpace(yPos, estimatedRecsHeight)) {
                yPos = addNewPage();
            }

            doc.setFontSize(12);
            if (currentAnalysisType === 'softSkills') {
                doc.setTextColor(124, 58, 237); // Purple for soft skills
            } else {
                doc.setTextColor(30, 58, 138); // Blue for digital skills
            }
            doc.text('Current Learning Path Recommendations', leftMargin, yPos);
            yPos += 20;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            for (const rec of activeData.recommendations) {
                const courseTitle = rec.course || '';
                const courseReason = rec.reason || '';

                // Calculate space needed for this recommendation
                const wrappedReason = doc.splitTextToSize(courseReason, contentWidth);
                const recHeight = 35 + (wrappedReason.length * 12);

                // Check if this recommendation fits on current page
                if (!checkPageSpace(yPos, recHeight)) {
                    yPos = addNewPage();
                }

                doc.setFont(undefined, 'bold');
                const wrappedTitle = doc.splitTextToSize(courseTitle, contentWidth);
                doc.text(wrappedTitle, leftMargin, yPos);
                yPos += (wrappedTitle.length * 12) + 5;

                doc.setFont(undefined, 'normal');
                doc.text(wrappedReason, leftMargin, yPos);
                yPos += (wrappedReason.length * 12) + 15;
            }
        }

        // Cross-Path Recommendations
        if (activeData.other_learning_paths_courses && activeData.other_learning_paths_courses.length > 0) {
            // Estimate space needed for cross-path recommendations
            const estimatedCrossPathHeight = 60 + (activeData.other_learning_paths_courses.length * 50);

            if (!checkPageSpace(yPos, estimatedCrossPathHeight)) {
                yPos = addNewPage();
            }

            doc.setFontSize(12);
            if (currentAnalysisType === 'softSkills') {
                doc.setTextColor(124, 58, 237); // Purple for soft skills
            } else {
                doc.setTextColor(30, 58, 138); // Blue for digital skills
            }
            doc.text('Additional Learning Path Recommendations', leftMargin, yPos);
            yPos += 20;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            for (const rec of activeData.other_learning_paths_courses) {
                const courseTitle = rec.course || '';
                const coursePath = rec.learningPath || '';
                const courseReason = rec.reason || '';

                // Calculate space needed for this recommendation
                const wrappedReason = doc.splitTextToSize(courseReason, contentWidth);
                const recHeight = 50 + (wrappedReason.length * 12);

                // Check if this recommendation fits on current page
                if (!checkPageSpace(yPos, recHeight)) {
                    yPos = addNewPage();
                }

                doc.setFont(undefined, 'bold');
                const wrappedTitle = doc.splitTextToSize(courseTitle, contentWidth);
                doc.text(wrappedTitle, leftMargin, yPos);
                yPos += (wrappedTitle.length * 12) + 5;

                // Add the learning path info if available
                if (coursePath) {
                    doc.setFont(undefined, 'italic');
                    const wrappedPath = doc.splitTextToSize(`Path: ${coursePath}`, contentWidth);
                    doc.text(wrappedPath, leftMargin, yPos);
                    yPos += (wrappedPath.length * 12) + 5;
                }

                doc.setFont(undefined, 'normal');
                doc.text(wrappedReason, leftMargin, yPos);
                yPos += (wrappedReason.length * 12) + 15;
            }
        }

        // Add footer with generated date on every page
        const totalPages = doc.internal.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            const now = new Date();
            const dateStr = now.toLocaleDateString();
            doc.setFontSize(8);
            doc.setTextColor(128, 128, 128);
            doc.text(`Generated on ${dateStr}`, pageWidth - rightMargin, pageHeight - 15, { align: 'right' });
            doc.text(`Page ${i} of ${totalPages}`, leftMargin, pageHeight - 15);
        }

        doc.save(`${analysisTypeText.replace(/\s+/g, '_')}_Gap_Analysis_Report.pdf`);
    }

    // Public API
    global.showSkillsGapAnalysis = showSkillsGapAnalysis;
})(typeof window !== 'undefined' ? window : global);